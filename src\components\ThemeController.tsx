'use client'

import { useEffect } from 'react'
import { useThemeStore, Theme } from '@/stores'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '@/components/ui/Dropdown'
import { cn } from '@/lib/utils'

// Theme option configuration with enhanced TypeScript types
interface ThemeOption {
  value: Theme
  label: string
  description: string
  icon: string
  iconColor: string
}

interface ThemeControllerProps {
  className?: string
  variant?: 'default' | 'compact'
}

const themeOptions: ThemeOption[] = [
  {
    value: 'light',
    label: 'Light',
    description: 'Light theme',
    icon: 'icon-[solar--sun-fog-bold-duotone]',
    iconColor: 'text-amber-500',
  },
  {
    value: 'dark',
    label: 'Dark',
    description: 'Dark theme',
    icon: 'icon-[solar--moon-stars-bold-duotone]',
    iconColor: 'text-violet-400',
  },
  {
    value: 'system',
    label: 'System',
    description: 'Follow system preference',
    icon: 'icon-[solar--monitor-bold-duotone]',
    iconColor: 'text-base-content',
  },
]

export function ThemeController({ className, variant = 'default' }: ThemeControllerProps = {}) {
  const { theme, resolvedTheme, setTheme, initializeTheme } = useThemeStore()

  useEffect(() => {
    initializeTheme()
  }, [initializeTheme])

  const handleThemeSelect = (selectedTheme: Theme) => {
    setTheme(selectedTheme)
  }

  const getCurrentIcon = (): string => {
    if (theme === 'system') {
      const isDark = resolvedTheme === 'dark'
      return isDark
        ? 'icon-[solar--moon-stars-bold-duotone] text-violet-400'
        : 'icon-[solar--sun-fog-bold-duotone] text-amber-500'
    }

    const currentOption = themeOptions.find((option) => option.value === theme)
    return currentOption
      ? `${currentOption.icon} ${currentOption.iconColor}`
      : 'icon-[solar--monitor-bold-duotone] text-base-content'
  }

  const getCurrentThemeLabel = (): string => {
    const currentOption = themeOptions.find((option) => option.value === theme)
    return currentOption?.label || 'Theme'
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className={cn(
            'btn btn-circle btn-outline border-base-300 transition-colors',
            'hover:bg-base-200 focus:bg-base-200',
            'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-base-100',
            variant === 'compact' && 'btn-sm',
            className
          )}
          aria-label={`Current theme: ${getCurrentThemeLabel()}. Click to change theme.`}
          title={`Current theme: ${getCurrentThemeLabel()}`}
        >
          <span className={getCurrentIcon()} aria-hidden="true" />
        </button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="end"
        sideOffset={8}
        className="w-48 p-2"
        aria-label="Theme selection menu"
      >
        <DropdownMenuRadioGroup
          value={theme}
          onValueChange={(value) => handleThemeSelect(value as Theme)}
        >
          {themeOptions.map((option) => (
            <DropdownMenuRadioItem
              key={option.value}
              value={option.value}
              className={cn(
                'flex items-center gap-3 px-3 py-2.5 cursor-pointer',
                'focus:bg-base-200 hover:bg-base-200',
                'data-[state=checked]:bg-primary data-[state=checked]:text-primary-content',
                'data-[state=checked]:hover:bg-primary data-[state=checked]:focus:bg-primary'
              )}
              aria-label={`${option.label} theme - ${option.description}`}
            >
              <span
                className={cn(
                  option.icon,
                  theme === option.value ? 'text-current' : option.iconColor
                )}
                aria-hidden="true"
              />
              <div className="flex flex-col items-start flex-1">
                <span className="font-medium text-sm">{option.label}</span>
                {variant === 'default' && (
                  <span className="text-xs opacity-70">{option.description}</span>
                )}
              </div>
            </DropdownMenuRadioItem>
          ))}
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
