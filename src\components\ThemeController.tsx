'use client'

import { useEffect } from 'react'
import { useThemeStore, Theme } from '@/stores'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '@/components/ui/Dropdown'

interface ThemeControllerProps {
  className?: string
  variant?: 'default' | 'compact'
}

export function ThemeController({ className, variant = 'default' }: ThemeControllerProps = {}) {
  const { theme, resolvedTheme, setTheme, initializeTheme } = useThemeStore()

  useEffect(() => {
    initializeTheme()
  }, [initializeTheme])

  const handleThemeSelect = (selectedTheme: Theme) => {
    setTheme(selectedTheme)
  }

  const getCurrentIcon = (): string => {
    if (theme === 'system') {
      const isDark = resolvedTheme === 'dark'
      return isDark
        ? 'icon-[solar--moon-stars-bold-duotone] text-violet-400'
        : 'icon-[solar--sun-fog-bold-duotone] text-amber-500'
    }
    if (theme === 'dark') return 'icon-[solar--moon-stars-bold-duotone] text-violet-400'
    if (theme === 'light') return 'icon-[solar--sun-fog-bold-duotone] text-amber-500'
    return 'icon-[solar--monitor-bold-duotone] text-base-content'
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className={`btn btn-circle btn-outline border-base-300 transition-colors hover:bg-base-200 focus:bg-base-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-base-100 ${
            variant === 'compact' ? 'btn-sm' : ''
          } ${className || ''}`}
          aria-label={`Current theme: ${theme}. Click to change theme.`}
          title={`Current theme: ${theme}`}
        >
          <span className={getCurrentIcon()} aria-hidden="true" />
        </button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="end"
        sideOffset={8}
        className="w-48 p-2"
        aria-label="Theme selection menu"
      >
        <DropdownMenuRadioGroup
          value={theme}
          onValueChange={(value) => handleThemeSelect(value as Theme)}
        >
          <DropdownMenuRadioItem
            value="light"
            className="flex items-center gap-3 px-3 py-2.5 cursor-pointer focus:bg-base-200 hover:bg-base-200 data-[state=checked]:bg-primary data-[state=checked]:text-primary-content data-[state=checked]:hover:bg-primary data-[state=checked]:focus:bg-primary"
            aria-label="Light theme"
          >
            <span
              className={`icon-[solar--sun-fog-bold-duotone] ${
                theme === 'light' ? 'text-current' : 'text-amber-500'
              }`}
              aria-hidden="true"
            />
            <span className="font-medium text-sm">Light</span>
          </DropdownMenuRadioItem>

          <DropdownMenuRadioItem
            value="dark"
            className="flex items-center gap-3 px-3 py-2.5 cursor-pointer focus:bg-base-200 hover:bg-base-200 data-[state=checked]:bg-primary data-[state=checked]:text-primary-content data-[state=checked]:hover:bg-primary data-[state=checked]:focus:bg-primary"
            aria-label="Dark theme"
          >
            <span
              className={`icon-[solar--moon-stars-bold-duotone] ${
                theme === 'dark' ? 'text-current' : 'text-violet-400'
              }`}
              aria-hidden="true"
            />
            <span className="font-medium text-sm">Dark</span>
          </DropdownMenuRadioItem>

          <DropdownMenuRadioItem
            value="system"
            className="flex items-center gap-3 px-3 py-2.5 cursor-pointer focus:bg-base-200 hover:bg-base-200 data-[state=checked]:bg-primary data-[state=checked]:text-primary-content data-[state=checked]:hover:bg-primary data-[state=checked]:focus:bg-primary"
            aria-label="System theme"
          >
            <span
              className={`icon-[solar--monitor-bold-duotone] ${
                theme === 'system' ? 'text-current' : 'text-base-content'
              }`}
              aria-hidden="true"
            />
            <span className="font-medium text-sm">System</span>
          </DropdownMenuRadioItem>
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
